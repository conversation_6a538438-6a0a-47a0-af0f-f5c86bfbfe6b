# Testing Setup

This project uses Vitest for testing React components and utilities.

## Test Structure

- **Unit Tests**: Located in `__tests__` folders next to the components they test
- **Test Utilities**: Located in `src/test/utils.tsx` - provides custom render function with providers
- **Test Setup**: Located in `src/test/setup.ts` - global test configuration and mocks

## Running Tests

```bash
# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

## Writing Tests

### Component Tests

Use the custom render function from `@/test/utils` which includes all necessary providers:

```tsx
import { render, screen, fireEvent } from '@/test/utils'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />)
    expect(screen.getByText('Hello')).toBeInTheDocument()
  })
})
```

### Testing Patterns

1. **Component Rendering**: Test that components render with correct content
2. **User Interactions**: Test click events, form submissions, etc.
3. **Conditional Rendering**: Test different states and props
4. **Error Handling**: Test error boundaries and error states
5. **Accessibility**: Test ARIA attributes and keyboard navigation

### Mocking

Common mocks are set up in `setup.ts`:
- localStorage/sessionStorage
- window.location
- ResizeObserver
- IntersectionObserver

For component-specific mocks, use `vi.fn()` and `vi.mock()` from Vitest.

## Test Coverage

The following components have test coverage:
- ✅ ErrorBoundary
- ✅ Button (UI component)
- ✅ TaskTable

## Adding New Tests

1. Create a `__tests__` folder next to your component
2. Create a test file with `.test.tsx` extension
3. Import the component and test utilities
4. Write descriptive test cases
5. Run tests to ensure they pass

## Best Practices

- Use descriptive test names
- Test user behavior, not implementation details
- Keep tests focused and isolated
- Use proper cleanup with testing library
- Mock external dependencies appropriately
