// components/tasks/TaskList.tsx

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "../ui/button";
import TaskDetails from "@/components/tasks/TaskDetails";
import TaskModal from "@/components/tasks/TaskModal";
import TaskTable from "@/components/tasks/TaskTable";
import { Task } from "@/services/task.service";
import { useAtom } from "jotai";
import { organizationsAtom } from "@/atoms/organizations";
import { userAtom } from "@/atoms/user";
import { useTasks } from "@/hooks/useTasks";
import ErrorBoundary from "../common/ErrorBoundary";

interface TaskListProps {
  orgId?: string; // Optional - if provided, only shows tasks for that organization
}

export default function TaskList({ orgId }: TaskListProps) {
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editTask, setEditTask] = useState<Task | null>(null);
  const [organizations] = useAtom(organizationsAtom);
  const [currentUser] = useAtom(userAtom);

  // Filtering and sorting
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Check if user is owner of the current organization
  const currentOrg = organizations.find((org) => org.id === orgId);
  const isOwner = currentOrg?.isOwner ?? false;

  // Use the tasks hook with status filter
  const {
    tasks = [],
    isLoading,
    isCreating,
    isUpdating,
    isUpdatingStatus,
    isDeleting,
    prefetchAllTasks,
  } = useTasks({
    orgId,
    filters: {
      status: statusFilter === "all" ? undefined : statusFilter,
    },
  });

  // Prefetch all tasks when component mounts
  useEffect(() => {
    if (!orgId) {
      prefetchAllTasks();
    }
  }, [orgId, prefetchAllTasks]);

  const handleCreateTask = () => {
    setEditTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task: Task) => {
    setEditTask(task);
    setShowTaskForm(true);
  };

  const handleTaskFormSubmit = () => {
    setShowTaskForm(false);
  };

  const handleTaskFormCancel = () => {
    setShowTaskForm(false);
    setEditTask(null);
  };

  // Filter tasks by search term using useMemo for performance
  const filteredTasks = useMemo(() => {
    if (!searchTerm) return tasks;

    const term = searchTerm.toLowerCase();
    return tasks.filter(
      (task) =>
        task.title.toLowerCase().includes(term) ||
        (task.description && task.description.toLowerCase().includes(term))
    );
  }, [tasks, searchTerm]);

  // Check if user can edit a task
  const canEditTask = (task: Task) => {
    // User can edit if they created the task or if they're the org owner
    const isTaskCreator = task.createdBy === currentUser?.id;

    // Check if user is org owner - use current org context if available, otherwise task's org
    let isOrgOwner = false;
    if (orgId && currentOrg) {
      // We're viewing tasks for a specific org
      isOrgOwner =
        currentOrg.members?.some(
          (member) => member.id === currentUser?.id && member.isOwner
        ) || false;
    } else {
      // We're viewing all tasks, check each task's organization
      isOrgOwner = task.organization?.createdBy === currentUser?.id;
    }

    return isTaskCreator || isOrgOwner;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-10">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">
            {orgId ? "Organization Tasks" : "My Tasks"}
          </h2>
          {(isOwner || !orgId) && (
            <Button
              className="cursor-pointer"
              onClick={handleCreateTask}
              disabled={isCreating}
            >
              {isCreating ? "Creating..." : "Create Task"}
            </Button>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 bg-[#212121] border border-[#404040] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full p-2 bg-[#212121] border border-[#404040] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        <TaskTable
          tasks={filteredTasks}
          orgId={orgId}
          onViewTask={setSelectedTask}
          onEditTask={handleEditTask}
          canEditTask={canEditTask}
          isUpdating={isUpdating}
          isUpdatingStatus={isUpdatingStatus}
          isDeleting={isDeleting}
        />
      </div>

      {/* Task Form Modal */}
      <TaskModal
        isOpen={showTaskForm}
        onClose={handleTaskFormCancel}
        orgId={orgId}
        editTask={editTask}
        onSubmit={handleTaskFormSubmit}
      />

      {/* Task Details Modal */}
      {selectedTask && (
        <TaskDetails
          taskId={selectedTask}
          onClose={() => setSelectedTask(null)}
          onUpdate={handleTaskFormSubmit}
          onDelete={() => {
            setSelectedTask(null);
          }}
          isOwner={isOwner}
        />
      )}
    </ErrorBoundary>
  );
}
