import { Task } from "@/services/task.service";
import TaskForm from "../forms/TaskForm";

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  orgId?: string;
  editTask?: Task | null;
  onSubmit: () => void;
  onDelete?: () => void;
}

const transformTaskForForm = (task: Task | null) => {
  if (!task) return undefined;
  return {
    id: task.id,
    title: task.title,
    description: task.description,
    status: task.status as "pending" | "in_progress" | "completed",
    dueAt: task.dueAt,
    assignedTo: task.assignee?.id,
    orgId: task.organization?.id,
  };
};

export default function TaskModal({
  isOpen,
  onClose,
  orgId,
  editTask,
  onSubmit,
  onDelete,
}: TaskModalProps) {
  if (!isOpen) return null;

  const handleSubmit = () => {
    onSubmit();
    onClose();
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-[#171717] border border-[#333333] rounded-lg p-6 w-full max-w-2xl">
        <TaskForm
          orgId={orgId}
          initialData={transformTaskForForm(editTask)}
          onSubmit={handleSubmit}
          onCancel={onClose}
          onDelete={editTask ? handleDelete : undefined}
        />
      </div>
    </div>
  );
}
