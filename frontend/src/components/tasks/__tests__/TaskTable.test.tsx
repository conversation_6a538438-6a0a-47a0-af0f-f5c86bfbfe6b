import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@/test/utils'
import TaskTable from '../TaskTable'
import { Task } from '@/services/task.service'

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'Test description',
    status: 'pending',
    dueAt: '2024-01-15T10:00:00Z',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'user1',
    assignee: {
      id: 'user2',
      fullName: '<PERSON>',
      email: '<EMAIL>',
    },
    organization: {
      id: 'org1',
      name: 'Test Org',
      createdBy: 'user1',
    },
  },
  {
    id: '2',
    title: 'Test Task 2',
    description: 'Another test description',
    status: 'completed',
    dueAt: '2024-01-20T10:00:00Z',
    createdAt: '2024-01-02T10:00:00Z',
    updatedAt: '2024-01-02T10:00:00Z',
    createdBy: 'user1',
    assignee: null,
    organization: {
      id: 'org1',
      name: 'Test Org',
      createdBy: 'user1',
    },
  },
]

const defaultProps = {
  tasks: mockTasks,
  onViewTask: vi.fn(),
  onEditTask: vi.fn(),
  canEditTask: vi.fn(() => true),
  isUpdating: false,
  isUpdatingStatus: false,
  isDeleting: false,
}

describe('TaskTable', () => {
  it('renders tasks correctly', () => {
    render(<TaskTable {...defaultProps} />)

    expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    expect(screen.getByText('Test Task 2')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Unassigned')).toBeInTheDocument()
  })

  it('shows organization column when not in org context', () => {
    render(<TaskTable {...defaultProps} />)

    expect(screen.getByText('Organization')).toBeInTheDocument()
    expect(screen.getAllByText('Test Org')).toHaveLength(2)
  })

  it('hides organization column when in org context', () => {
    render(<TaskTable {...defaultProps} orgId="org1" />)

    expect(screen.queryByText('Organization')).not.toBeInTheDocument()
  })

  it('calls onViewTask when view button is clicked', () => {
    const onViewTask = vi.fn()
    render(<TaskTable {...defaultProps} onViewTask={onViewTask} />)

    const viewButtons = screen.getAllByText('View')
    fireEvent.click(viewButtons[0])

    expect(onViewTask).toHaveBeenCalledWith('1')
  })

  it('calls onEditTask when edit button is clicked', () => {
    const onEditTask = vi.fn()
    render(<TaskTable {...defaultProps} onEditTask={onEditTask} />)

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(onEditTask).toHaveBeenCalledWith(mockTasks[0])
  })

  it('shows edit button only when user can edit task', () => {
    const canEditTask = vi.fn((task) => task.id === '1')
    render(<TaskTable {...defaultProps} canEditTask={canEditTask} />)

    const editButtons = screen.getAllByText('Edit')
    expect(editButtons).toHaveLength(1)
  })

  it('shows no tasks message when tasks array is empty', () => {
    render(<TaskTable {...defaultProps} tasks={[]} />)

    expect(screen.getByText('No tasks found.')).toBeInTheDocument()
  })
})
