//frontend/src/components/dashboard/Organizations.tsx
import { motion } from "framer-motion";
import { Plus } from "lucide-react";
import { Button } from "../ui/button";
import { useState, useEffect } from "react";
import Modal from "../ui/Modal";
import CreateOrgForm from "../forms/CreateOrgForm";
import { Organization } from "@/services/organization.service";
import { toast } from "../hooks/use-toast";
import { isUserOrgOwner, organizationsAtom } from "@/atoms/organizations";
import { useAuth } from "@/hooks/useAuth";
import { useNavigate } from "react-router-dom";
import { useOrganizations } from "@/hooks/useOrganizations";
import { useAtom } from "jotai";

export default function Organizations() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();
  const [, setOrganizations] = useAtom(organizationsAtom);

  const { organizations, isLoading, error, createOrganization, isCreating } =
    useOrganizations();

  // Update the atom when organizations data changes
  useEffect(() => {
    if (organizations) {
      setOrganizations(organizations);
    }
  }, [organizations, setOrganizations]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Failed to load organizations</p>
      </div>
    );
  }

  const handleCreateOrg = (data: any) => {
    createOrganization(data, {
      onSuccess: () => {
        setIsCreateModalOpen(false);
        toast({
          title: "Success",
          description: "Organization created successfully",
          type: "success",
        });
      },
      onError: (error) => {
        console.error("Failed to create organization:", error);
        toast({
          title: "Error",
          description: "Failed to create organization",
          type: "error",
        });
      },
    });
  };

  const handleOrgClick = (orgId: string) => {
    navigate(`/dashboard/organizations/${orgId}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-white">Organizations</h2>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          disabled={isCreating}
          className="cursor-pointer"
        >
          <Plus className="h-4 w-4 mr-2" />
          {isCreating ? "Creating..." : "Create Organization"}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {organizations?.map((org: Organization) => (
          <motion.div
            key={org.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-[#404040] rounded-lg p-6 cursor-pointer hover:bg-[#4a4a4a] transition-colors"
            onClick={() => handleOrgClick(org.id)}
          >
            <h3 className="text-lg font-semibold text-white mb-2">
              {org.name}
            </h3>
            <p className="text-gray-400 text-sm mb-4">
              {org.description || "No description"}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">
                {org.members?.length || 0} members
              </span>
              {isUserOrgOwner(org, user?.id) && (
                <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                  Owner
                </span>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create Organization"
      >
        <CreateOrgForm
          onSubmit={handleCreateOrg}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>
    </div>
  );
}
